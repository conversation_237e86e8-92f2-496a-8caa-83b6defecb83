{"manifest_version": 3, "name": "Picky - Color Picker", "version": "1.0.0", "description": "Pick colors from any website and save them in multiple formats (HEX, RGB, HSL, HSV)", "permissions": ["activeTab", "storage", "clipboardWrite", "scripting"], "action": {"default_popup": "popup.html", "default_title": "Picky Color Picker", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "css": ["content.css"], "run_at": "document_end"}], "background": {"service_worker": "background.js"}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["color-utils.js"], "matches": ["<all_urls>"]}]}