// Color conversion utilities for the Picky color picker extension

// Prevent duplicate declarations
if (typeof ColorUtils === 'undefined') {

class ColorUtils {
    /**
     * Convert RGB values to HEX format
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @returns {string} HEX color string
     */
    static rgbToHex(r, g, b) {
        const toHex = (n) => {
            const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }

    /**
     * Convert HEX to RGB values
     * @param {string} hex - HEX color string
     * @returns {object} RGB object with r, g, b properties
     */
    static hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    /**
     * Convert RGB to HSL
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @returns {object} HSL object with h, s, l properties
     */
    static rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    /**
     * Convert RGB to HSV
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @returns {object} HSV object with h, s, v properties
     */
    static rgbToHsv(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, v = max;

        const d = max - min;
        s = max === 0 ? 0 : d / max;

        if (max === min) {
            h = 0; // achromatic
        } else {
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            v: Math.round(v * 100)
        };
    }

    /**
     * Format color value based on the specified format
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @param {string} format - Format type ('hex', 'rgb', 'hsl', 'hsv')
     * @returns {string} Formatted color string
     */
    static formatColor(r, g, b, format) {
        switch (format.toLowerCase()) {
            case 'hex':
                return this.rgbToHex(r, g, b);
            
            case 'rgb':
                return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
            
            case 'hsl':
                const hsl = this.rgbToHsl(r, g, b);
                return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
            
            case 'hsv':
                const hsv = this.rgbToHsv(r, g, b);
                return `hsv(${hsv.h}, ${hsv.s}%, ${hsv.v}%)`;
            
            default:
                return this.rgbToHex(r, g, b);
        }
    }

    /**
     * Get color from pixel data
     * @param {ImageData} imageData - Canvas image data
     * @param {number} x - X coordinate
     * @param {number} y - Y coordinate
     * @returns {object} Color object with r, g, b properties
     */
    static getColorFromImageData(imageData, x, y) {
        const index = (y * imageData.width + x) * 4;
        return {
            r: imageData.data[index],
            g: imageData.data[index + 1],
            b: imageData.data[index + 2],
            a: imageData.data[index + 3]
        };
    }

    /**
     * Check if a color is light or dark
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @returns {boolean} True if the color is light
     */
    static isLightColor(r, g, b) {
        // Calculate relative luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        return luminance > 0.5;
    }

    /**
     * Generate a contrasting text color (black or white) for a given background color
     * @param {number} r - Red value (0-255)
     * @param {number} g - Green value (0-255)
     * @param {number} b - Blue value (0-255)
     * @returns {string} Either '#000000' or '#FFFFFF'
     */
    static getContrastingTextColor(r, g, b) {
        return this.isLightColor(r, g, b) ? '#000000' : '#FFFFFF';
    }

    /**
     * Parse color string to RGB values
     * @param {string} colorString - Color string in various formats
     * @returns {object|null} RGB object or null if parsing fails
     */
    static parseColorString(colorString) {
        // Handle HEX format
        if (colorString.startsWith('#')) {
            return this.hexToRgb(colorString);
        }

        // Handle RGB format
        const rgbMatch = colorString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }

        return null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorUtils;
} else if (typeof window !== 'undefined') {
    window.ColorUtils = ColorUtils;
}

} // End of ColorUtils class guard
