// Popup script for Picky color picker extension

class PickyPopup {
    constructor() {
        this.currentColor = null;
        this.currentFormat = 'hex';
        this.savedColors = [];
        
        this.init();
    }

    async init() {
        // Load color utilities
        await this.loadColorUtils();
        
        // Initialize UI elements
        this.initializeElements();
        
        // Load saved colors
        await this.loadSavedColors();
        
        // Check for last picked color
        await this.checkLastPickedColor();
        
        // Add event listeners
        this.addEventListeners();
        
        // Listen for messages from background script
        this.listenForMessages();
    }

    async loadColorUtils() {
        return new Promise((resolve) => {
            if (typeof ColorUtils !== 'undefined') {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'color-utils.js';
            script.onload = resolve;
            document.head.appendChild(script);
        });
    }

    initializeElements() {
        this.pickColorBtn = document.getElementById('pickColorBtn');
        this.colorPreview = document.getElementById('colorPreview');
        this.colorSwatch = document.getElementById('colorSwatch');
        this.noColor = document.getElementById('noColor');
        this.colorInfo = document.getElementById('colorInfo');
        this.formatSelect = document.getElementById('formatSelect');
        this.colorValue = document.getElementById('colorValue');
        this.copyBtn = document.getElementById('copyBtn');
        this.saveColorBtn = document.getElementById('saveColorBtn');
        this.savedColorsGrid = document.getElementById('savedColorsGrid');
        this.noSavedColors = document.getElementById('noSavedColors');
        this.clearAllBtn = document.getElementById('clearAllBtn');
        this.statusMessage = document.getElementById('statusMessage');
    }

    addEventListeners() {
        // Pick color button
        this.pickColorBtn.addEventListener('click', () => this.startColorPicking());
        
        // Format selector
        this.formatSelect.addEventListener('change', (e) => {
            this.currentFormat = e.target.value;
            this.updateColorDisplay();
        });
        
        // Copy button
        this.copyBtn.addEventListener('click', () => this.copyCurrentColor());
        
        // Save color button
        this.saveColorBtn.addEventListener('click', () => this.saveCurrentColor());
        
        // Clear all button
        this.clearAllBtn.addEventListener('click', () => this.clearAllColors());
        
        // Color value input (for manual copying)
        this.colorValue.addEventListener('click', () => this.colorValue.select());
    }

    listenForMessages() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'colorPickedNotification') {
                this.handleColorPicked(request.color);
            }
        });
    }

    async startColorPicking() {
        try {
            console.log('Starting color picking...');
            this.pickColorBtn.disabled = true;
            this.pickColorBtn.textContent = 'Picking...';

            // Get current active tab
            console.log('Querying for active tab...');
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            console.log('Found tabs:', tabs);

            const tab = tabs[0];
            if (!tab || !tab.id) {
                throw new Error('No active tab found');
            }

            console.log('Active tab ID:', tab.id);

            // Send message to background script to start color picking
            console.log('Sending message to background script...');
            const response = await chrome.runtime.sendMessage({
                action: 'startColorPicking',
                tabId: tab.id
            });

            console.log('Background script response:', response);

            if (response && response.success) {
                console.log('Color picking started successfully, closing popup...');
                // Close popup to allow color picking
                window.close();
            } else {
                throw new Error('Failed to start color picking');
            }

        } catch (error) {
            console.error('Error starting color picking:', error);
            this.showStatus(`Error: ${error.message}`, 'error');
            this.pickColorBtn.disabled = false;
            this.pickColorBtn.innerHTML = '<span class="pick-icon">🎯</span>Pick Color';
        }
    }

    async handleColorPicked(color) {
        this.currentColor = color;
        this.updateColorDisplay();

        // Auto-copy HEX value to clipboard
        const hexValue = ColorUtils.formatColor(color.r, color.g, color.b, 'hex');
        const success = await this.copyToClipboard(hexValue);

        if (success) {
            this.showStatus(`Color picked! ${hexValue} copied to clipboard.`);
        } else {
            this.showStatus('Color picked! Click copy button to copy to clipboard.');
        }
    }

    updateColorDisplay() {
        if (!this.currentColor) {
            this.colorSwatch.style.display = 'none';
            this.noColor.style.display = 'block';
            this.colorInfo.style.display = 'none';
            return;
        }
        
        const { r, g, b } = this.currentColor;
        
        // Show color swatch
        this.colorSwatch.style.display = 'block';
        this.colorSwatch.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
        this.noColor.style.display = 'none';
        this.colorInfo.style.display = 'block';
        
        // Update color value based on selected format
        const formattedColor = ColorUtils.formatColor(r, g, b, this.currentFormat);
        this.colorValue.value = formattedColor;
        
        // Update format selector
        this.formatSelect.value = this.currentFormat;
    }

    async copyCurrentColor() {
        if (!this.currentColor) return;

        const colorText = this.colorValue.value;
        const success = await this.copyToClipboard(colorText);

        if (success) {
            this.showStatus(`Copied ${colorText}!`);

            // Add visual feedback to copy button
            const originalText = this.copyBtn.innerHTML;
            this.copyBtn.innerHTML = '✓';
            this.copyBtn.style.backgroundColor = '#4CAF50';

            setTimeout(() => {
                this.copyBtn.innerHTML = originalText;
                this.copyBtn.style.backgroundColor = '';
            }, 1000);
        } else {
            this.showStatus('Failed to copy to clipboard', 'error');
        }
    }

    async copyToClipboard(text) {
        try {
            // Try direct clipboard API first (works in popup)
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            console.error('Direct clipboard copy failed:', error);

            // Fallback: use background script
            try {
                await chrome.runtime.sendMessage({
                    action: 'copyToClipboard',
                    text: text
                });
                return true;
            } catch (fallbackError) {
                console.error('Background clipboard copy failed:', fallbackError);

                // Final fallback: create temporary input
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.opacity = '0';
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return true;
                } catch (finalError) {
                    console.error('All clipboard methods failed:', finalError);
                    return false;
                }
            }
        }
    }

    async saveCurrentColor() {
        if (!this.currentColor) return;
        
        try {
            await chrome.runtime.sendMessage({
                action: 'saveColor',
                color: this.currentColor
            });
            
            this.showStatus('Color saved!');
            await this.loadSavedColors();
            
        } catch (error) {
            console.error('Error saving color:', error);
            this.showStatus('Error saving color', 'error');
        }
    }

    async loadSavedColors() {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getSavedColors'
            });
            
            this.savedColors = response.colors || [];
            this.renderSavedColors();
            
        } catch (error) {
            console.error('Error loading saved colors:', error);
            this.savedColors = [];
            this.renderSavedColors();
        }
    }

    renderSavedColors() {
        // Clear existing colors
        this.savedColorsGrid.innerHTML = '';
        
        if (this.savedColors.length === 0) {
            this.noSavedColors.style.display = 'block';
            this.savedColorsGrid.appendChild(this.noSavedColors);
            this.clearAllBtn.style.display = 'none';
            return;
        }
        
        this.noSavedColors.style.display = 'none';
        this.clearAllBtn.style.display = 'block';
        
        this.savedColors.forEach(color => {
            const colorItem = this.createSavedColorItem(color);
            this.savedColorsGrid.appendChild(colorItem);
        });
    }

    createSavedColorItem(color) {
        const item = document.createElement('div');
        item.className = 'saved-color-item';
        item.style.backgroundColor = color.hex;
        item.title = color.hex;
        
        // Add click handler to copy color
        item.addEventListener('click', async () => {
            const success = await this.copyToClipboard(color.hex);
            if (success) {
                this.showStatus(`Copied ${color.hex}!`);

                // Add visual feedback to the clicked item
                const originalTransform = item.style.transform;
                item.style.transform = 'scale(1.2)';
                item.style.boxShadow = '0 4px 12px rgba(33, 150, 243, 0.4)';

                setTimeout(() => {
                    item.style.transform = originalTransform;
                    item.style.boxShadow = '';
                }, 300);
            } else {
                this.showStatus('Failed to copy color', 'error');
            }
        });
        
        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-btn';
        deleteBtn.innerHTML = '×';
        deleteBtn.title = 'Delete color';
        
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteColor(color.id);
        });
        
        item.appendChild(deleteBtn);
        return item;
    }

    async deleteColor(colorId) {
        try {
            await chrome.runtime.sendMessage({
                action: 'deleteColor',
                colorId: colorId
            });
            
            await this.loadSavedColors();
            this.showStatus('Color deleted');
            
        } catch (error) {
            console.error('Error deleting color:', error);
            this.showStatus('Error deleting color', 'error');
        }
    }

    async clearAllColors() {
        if (!confirm('Are you sure you want to delete all saved colors?')) {
            return;
        }
        
        try {
            await chrome.runtime.sendMessage({
                action: 'clearAllColors'
            });
            
            await this.loadSavedColors();
            this.showStatus('All colors cleared');
            
        } catch (error) {
            console.error('Error clearing colors:', error);
            this.showStatus('Error clearing colors', 'error');
        }
    }

    async checkLastPickedColor() {
        try {
            const data = await chrome.storage.local.get(['lastPickedColor']);
            if (data.lastPickedColor) {
                this.currentColor = data.lastPickedColor;
                this.updateColorDisplay();
                
                // Clear the last picked color
                await chrome.storage.local.remove(['lastPickedColor']);
            }
        } catch (error) {
            console.error('Error checking last picked color:', error);
        }
    }

    showStatus(message, type = 'success') {
        this.statusMessage.textContent = message;
        this.statusMessage.className = `status-message ${type} show`;
        
        setTimeout(() => {
            this.statusMessage.classList.remove('show');
        }, 3000);
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PickyPopup();
});
