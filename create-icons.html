<!DOCTYPE html>
<html>
<head>
    <title>Create Picky Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
    </style>
</head>
<body>
    <h1>Picky Extension Icons</h1>
    <p>This page generates the icons for the Picky color picker extension.</p>
    
    <div class="icon-container">
        <h3>16x16 Icon</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>32x32 Icon</h3>
        <canvas id="icon32" width="32" height="32"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>48x48 Icon</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>128x128 Icon</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
    </div>
    
    <button onclick="downloadIcons()">Download All Icons</button>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 48; // Base size is 48px
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Color picker dropper
            ctx.strokeStyle = '#ffffff';
            ctx.fillStyle = '#ffffff';
            ctx.lineWidth = 2 * scale;
            
            // Dropper body
            const centerX = size / 2;
            const centerY = size / 2;
            const dropperSize = size * 0.6;
            
            ctx.beginPath();
            ctx.moveTo(centerX - dropperSize/4, centerY - dropperSize/4);
            ctx.lineTo(centerX + dropperSize/4, centerY - dropperSize/4);
            ctx.lineTo(centerX + dropperSize/4, centerY + dropperSize/6);
            ctx.lineTo(centerX + dropperSize/6, centerY + dropperSize/4);
            ctx.lineTo(centerX - dropperSize/6, centerY + dropperSize/4);
            ctx.lineTo(centerX - dropperSize/4, centerY + dropperSize/6);
            ctx.closePath();
            ctx.fill();
            
            // Dropper tip
            ctx.beginPath();
            ctx.arc(centerX + dropperSize/3, centerY + dropperSize/3, 3*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Color sample
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(centerX - dropperSize/6, centerY - dropperSize/6, 4*scale, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadIcons() {
            downloadCanvas(document.getElementById('icon16'), 'icon16.png');
            downloadCanvas(document.getElementById('icon32'), 'icon32.png');
            downloadCanvas(document.getElementById('icon48'), 'icon48.png');
            downloadCanvas(document.getElementById('icon128'), 'icon128.png');
        }
        
        // Draw all icons
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon32'), 32);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
    </script>
</body>
</html>
