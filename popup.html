<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Picky Color Picker</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 Picky</h1>
            <button id="pickColorBtn" class="pick-btn">
                <span class="pick-icon">🎯</span>
                Pick Color
            </button>
        </div>

        <!-- Current Color Display -->
        <div class="current-color-section">
            <div class="color-preview" id="colorPreview">
                <div class="color-swatch" id="colorSwatch"></div>
                <div class="no-color" id="noColor">No color picked</div>
            </div>
            
            <div class="color-info" id="colorInfo" style="display: none;">
                <div class="format-selector">
                    <select id="formatSelect">
                        <option value="hex">HEX</option>
                        <option value="rgb">RGB</option>
                        <option value="hsl">HSL</option>
                        <option value="hsv">HSV</option>
                    </select>
                </div>
                
                <div class="color-value-container">
                    <input type="text" id="colorValue" readonly>
                    <button id="copyBtn" class="copy-btn" title="Copy to clipboard">
                        📋
                    </button>
                </div>
                
                <button id="saveColorBtn" class="save-btn">
                    💾 Save Color
                </button>
            </div>
        </div>

        <!-- Saved Colors Section -->
        <div class="saved-colors-section">
            <div class="section-header">
                <h3>Saved Colors</h3>
                <button id="clearAllBtn" class="clear-all-btn" title="Clear all saved colors">
                    🗑️
                </button>
            </div>
            
            <div class="saved-colors-grid" id="savedColorsGrid">
                <div class="no-saved-colors" id="noSavedColors">
                    No saved colors yet
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div class="status-message" id="statusMessage"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
