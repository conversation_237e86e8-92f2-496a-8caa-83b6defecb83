# 🔧 Picky Color Picker - Troubleshooting Guide

## Quick Fix for "Cannot read properties of undefined (reading 'id')" Error

This error typically occurs when the extension can't access the current tab. Here's how to fix it:

### Step 1: Reload the Extension
1. Go to `chrome://extensions/`
2. Find "Picky - Color Picker"
3. Click the refresh/reload button (🔄)
4. Try the color picker again

### Step 2: Check Permissions
1. Go to `chrome://extensions/`
2. Click "Details" on the Picky extension
3. Make sure these permissions are granted:
   - ✅ Read and change all your data on all websites
   - ✅ Store unlimited amount of client-side data

### Step 3: Test on a Regular Website
- ❌ Don't test on `chrome://` pages (like chrome://extensions/)
- ❌ Don't test on `about:` pages
- ✅ Test on regular websites like `google.com`, `github.com`, etc.
- ✅ Use the included `test-page.html` file

### Step 4: Check Console for Debug Messages
1. Open the test page: `file:///[path-to-extension]/test-page.html`
2. Open Developer Tools (F12)
3. Click the Picky extension icon
4. Click "Pick Color"
5. Check console for these messages:

**Expected in Popup Console:**
```
Starting color picking...
Querying for active tab...
Found tabs: [...]
Active tab ID: [number]
```

**Expected in Background Console:**
```
Background received message: {action: 'startColorPicking', tabId: [number]}
Using tab: {id: [number]}
Starting color picking for tab: [number]
```

## Common Issues & Solutions

### Issue 1: Extension Icon Not Visible
**Solution:**
1. Look for the puzzle piece icon (🧩) in Chrome toolbar
2. Click it to see hidden extensions
3. Pin the Picky extension

### Issue 2: "Pick Color" Button Does Nothing
**Possible Causes:**
- Extension not properly loaded
- Missing permissions
- JavaScript errors

**Solution:**
1. Check browser console for errors
2. Reload the extension
3. Make sure you're on a regular webpage

### Issue 3: Color Picker Overlay Not Appearing
**Possible Causes:**
- Content script injection failed
- CSS not loaded
- Website blocking scripts

**Solution:**
1. Check if content script is injected:
   - Open Developer Tools
   - Go to Sources tab
   - Look for `content.js` in the page sources
2. Try on a different website
3. Check for CSP (Content Security Policy) restrictions

### Issue 4: Colors Not Copying to Clipboard
**Possible Causes:**
- Browser clipboard permissions
- Website security restrictions

**Solution:**
1. Try clicking the copy button manually
2. Check if clipboard permission is granted
3. Try on a different website

### Issue 5: Saved Colors Not Persisting
**Possible Causes:**
- Storage permission not granted
- Browser in incognito mode
- Storage quota exceeded

**Solution:**
1. Check storage permission in extension details
2. Don't use incognito mode for testing
3. Clear some saved colors if you have many

## Debug Mode Instructions

### Enable Debug Logging
The extension includes debug logging. To see detailed logs:

1. **For Popup Logs:**
   - Right-click the extension icon
   - Select "Inspect popup"
   - Go to Console tab

2. **For Background Script Logs:**
   - Go to `chrome://extensions/`
   - Click "Details" on Picky extension
   - Click "Inspect views: background page"
   - Go to Console tab

3. **For Content Script Logs:**
   - Open Developer Tools on the webpage (F12)
   - Go to Console tab

### Test Sequence
1. Open `debug-test.html` in Chrome
2. Open all three console windows (popup, background, content)
3. Click Picky extension icon
4. Click "Pick Color"
5. Watch for debug messages in all consoles

## Manual Installation Verification

### Check File Structure
Make sure you have all these files:
```
picky/
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── content.js
├── content.css
├── background.js
├── color-utils.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── [other files]
```

### Verify Manifest
Check that `manifest.json` contains:
- `"manifest_version": 3`
- All required permissions
- Correct file references

### Test Basic Functionality
Run this in any webpage console:
```javascript
// Test if extension is loaded
chrome.runtime.sendMessage({action: 'test'}, (response) => {
    console.log('Extension accessible:', !!chrome.runtime.id);
});
```

## Advanced Debugging

### Check Extension Errors
1. Go to `chrome://extensions/`
2. Enable "Developer mode"
3. Look for error messages under the extension

### Inspect Network Requests
1. Open Developer Tools
2. Go to Network tab
3. Try using the color picker
4. Look for failed requests

### Check Security Policies
Some websites have strict Content Security Policies that might block the extension:
1. Open Developer Tools
2. Go to Console tab
3. Look for CSP violation messages
4. Try on a different website

## Getting Help

If none of these solutions work:

1. **Check Browser Version:**
   - Chrome 88+ required
   - Update Chrome if needed

2. **Try Different Websites:**
   - Test on multiple sites
   - Some sites may have restrictions

3. **Restart Browser:**
   - Close all Chrome windows
   - Restart Chrome
   - Try again

4. **Reinstall Extension:**
   - Remove the extension
   - Restart Chrome
   - Install again

## Contact Information

If you're still having issues, please provide:
- Chrome version
- Operating system
- Exact error messages
- Steps to reproduce the problem
- Console logs from all three contexts (popup, background, content)

---

Most issues are resolved by reloading the extension and testing on a regular webpage! 🎨
