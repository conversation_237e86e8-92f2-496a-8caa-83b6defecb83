// Background service worker for Picky color picker extension

class PickyBackground {
    constructor() {
        this.init();
    }

    init() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.handleInstall();
            } else if (details.reason === 'update') {
                this.handleUpdate(details.previousVersion);
            }
        });

        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle action button click (when user clicks extension icon)
        chrome.action.onClicked.addListener((tab) => {
            this.handleActionClick(tab);
        });
    }

    handleInstall() {
        console.log('Picky Color Picker installed');
        
        // Initialize default settings
        chrome.storage.local.set({
            savedColors: [],
            defaultFormat: 'hex',
            autoClipboard: true
        });
    }

    handleUpdate(previousVersion) {
        console.log(`Picky Color Picker updated from ${previousVersion}`);
        
        // Handle any migration logic here if needed
        this.migrateData(previousVersion);
    }

    async migrateData(previousVersion) {
        try {
            const data = await chrome.storage.local.get(['savedColors']);
            
            // Ensure savedColors is an array
            if (!Array.isArray(data.savedColors)) {
                await chrome.storage.local.set({ savedColors: [] });
            }
            
            // Add any version-specific migration logic here
            
        } catch (error) {
            console.error('Error migrating data:', error);
        }
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            console.log('Background received message:', request);
            console.log('Sender:', sender);

            switch (request.action) {
                case 'ping':
                    console.log('Background script ping received');
                    sendResponse({ success: true, message: 'Background script is active' });
                    return true;
                    break;

                case 'startColorPicking':
                    // Get tab from request.tabId or sender.tab
                    const tab = request.tabId ? { id: request.tabId } : sender.tab;
                    console.log('Using tab:', tab);
                    await this.startColorPicking(tab);
                    sendResponse({ success: true });
                    return true; // Keep message channel open for async response
                    break;

                case 'colorPicked':
                    await this.handleColorPicked(request.color, sender.tab);
                    sendResponse({ success: true });
                    return true;
                    break;

                case 'saveColor':
                    await this.saveColor(request.color);
                    sendResponse({ success: true });
                    return true;
                    break;

                case 'getSavedColors':
                    const colors = await this.getSavedColors();
                    sendResponse({ colors });
                    return true;
                    break;

                case 'deleteColor':
                    await this.deleteColor(request.colorId);
                    sendResponse({ success: true });
                    return true;
                    break;

                case 'clearAllColors':
                    await this.clearAllColors();
                    sendResponse({ success: true });
                    return true;
                    break;

                case 'copyToClipboard':
                    await this.copyToClipboard(request.text);
                    sendResponse({ success: true });
                    return true;
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
                    return true;
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }

    async handleActionClick(tab) {
        // This is called when user clicks the extension icon
        // The popup will handle the UI, so we don't need to do anything here
        console.log('Extension icon clicked');
    }

    async startColorPicking(tab) {
        try {
            if (!tab || !tab.id) {
                throw new Error('Invalid tab provided');
            }

            console.log('Starting color picking for tab:', tab.id);

            // Try to ping first to see if content script exists
            let needsInjection = true;
            try {
                console.log('Testing if content script exists...');
                const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
                if (response && response.success) {
                    console.log('Content script already exists');
                    needsInjection = false;
                }
            } catch (error) {
                console.log('Content script not found, will inject');
                needsInjection = true;
            }

            if (needsInjection) {
                console.log('Injecting content scripts...');

                // Inject color utilities first
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['color-utils.js']
                });

                // Then inject content script
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });

                // Inject CSS
                await chrome.scripting.insertCSS({
                    target: { tabId: tab.id },
                    files: ['content.css']
                });

                // Wait for scripts to load and initialize
                console.log('Waiting for scripts to initialize...');
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Send message to start color picking with retry logic
            console.log('Sending start color picking message...');
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                try {
                    // Check if tab still exists
                    const currentTab = await chrome.tabs.get(tab.id);
                    if (!currentTab) {
                        throw new Error('Tab no longer exists');
                    }

                    await chrome.tabs.sendMessage(tab.id, { action: 'startColorPicking' });
                    console.log('Color picking started successfully');
                    return;
                } catch (error) {
                    attempts++;
                    console.log(`Attempt ${attempts} failed:`, error.message);

                    // If it's a context invalidation error, don't retry
                    if (error.message.includes('Extension context invalidated') ||
                        error.message.includes('receiving end does not exist')) {
                        console.log('Extension context issue detected, re-injecting scripts...');

                        // Re-inject scripts
                        try {
                            await chrome.scripting.executeScript({
                                target: { tabId: tab.id },
                                files: ['color-utils.js', 'content.js']
                            });
                            await new Promise(resolve => setTimeout(resolve, 300));
                        } catch (injectionError) {
                            console.error('Failed to re-inject scripts:', injectionError);
                            break;
                        }
                    }

                    if (attempts < maxAttempts) {
                        console.log('Retrying in 200ms...');
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                }
            }

            throw new Error('Failed to start color picking after multiple attempts');

        } catch (error) {
            console.error('Error starting color picking:', error);
            throw error;
        }
    }

    async handleColorPicked(color, tab) {
        try {
            // Store the picked color temporarily
            await chrome.storage.local.set({ lastPickedColor: color });
            
            // Notify popup about the picked color
            chrome.runtime.sendMessage({
                action: 'colorPickedNotification',
                color: color
            });
            
        } catch (error) {
            console.error('Error handling picked color:', error);
        }
    }

    async saveColor(color) {
        try {
            const data = await chrome.storage.local.get(['savedColors']);
            const savedColors = data.savedColors || [];
            
            // Create color object with timestamp and ID
            const colorObject = {
                id: Date.now().toString(),
                r: color.r,
                g: color.g,
                b: color.b,
                hex: this.rgbToHex(color.r, color.g, color.b),
                timestamp: Date.now()
            };
            
            // Check if color already exists
            const existingIndex = savedColors.findIndex(c => c.hex === colorObject.hex);
            
            if (existingIndex >= 0) {
                // Update timestamp of existing color
                savedColors[existingIndex].timestamp = Date.now();
            } else {
                // Add new color to the beginning of the array
                savedColors.unshift(colorObject);
                
                // Limit to 50 saved colors
                if (savedColors.length > 50) {
                    savedColors.splice(50);
                }
            }
            
            await chrome.storage.local.set({ savedColors });
            
        } catch (error) {
            console.error('Error saving color:', error);
            throw error;
        }
    }

    async getSavedColors() {
        try {
            const data = await chrome.storage.local.get(['savedColors']);
            return data.savedColors || [];
        } catch (error) {
            console.error('Error getting saved colors:', error);
            return [];
        }
    }

    async deleteColor(colorId) {
        try {
            const data = await chrome.storage.local.get(['savedColors']);
            const savedColors = data.savedColors || [];
            
            const filteredColors = savedColors.filter(color => color.id !== colorId);
            await chrome.storage.local.set({ savedColors: filteredColors });
            
        } catch (error) {
            console.error('Error deleting color:', error);
            throw error;
        }
    }

    async clearAllColors() {
        try {
            await chrome.storage.local.set({ savedColors: [] });
        } catch (error) {
            console.error('Error clearing all colors:', error);
            throw error;
        }
    }

    async copyToClipboard(text) {
        try {
            // Use the clipboard API through a content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: (textToCopy) => {
                    navigator.clipboard.writeText(textToCopy).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = textToCopy;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                    });
                },
                args: [text]
            });
            
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            throw error;
        }
    }

    // Utility function for color conversion
    rgbToHex(r, g, b) {
        const toHex = (n) => {
            const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }
}

// Initialize the background service
new PickyBackground();
