/* Color Picker Overlay Styles */
.picky-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2147483647 !important;
    pointer-events: none !important;
    background: transparent !important;
}

.picky-overlay.active {
    pointer-events: all !important;
    cursor: crosshair !important;
}

.picky-magnifier {
    position: fixed !important;
    width: 120px !important;
    height: 120px !important;
    border: 3px solid #fff !important;
    border-radius: 50% !important;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    pointer-events: none !important;
    z-index: 2147483648 !important;
    display: none !important;
    overflow: hidden !important;
    background: #fff !important;
}

.picky-magnifier.active {
    display: block !important;
}

.picky-magnifier-canvas {
    width: 100% !important;
    height: 100% !important;
    border-radius: 50% !important;
}

.picky-crosshair {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 2px !important;
    height: 20px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    transform: translate(-50%, -50%) !important;
    pointer-events: none !important;
}

.picky-crosshair::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 20px !important;
    height: 2px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    transform: translate(-50%, -50%) !important;
}

.picky-color-info {
    position: fixed !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    z-index: 2147483649 !important;
    pointer-events: none !important;
    display: none !important;
    white-space: nowrap !important;
}

.picky-color-info.active {
    display: block !important;
}

.picky-color-preview {
    display: inline-block !important;
    width: 16px !important;
    height: 16px !important;
    border-radius: 2px !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
}

.picky-instructions {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    z-index: 2147483649 !important;
    pointer-events: none !important;
    display: none !important;
    text-align: center !important;
}

.picky-instructions.active {
    display: block !important;
}

.picky-instructions .key {
    background: rgba(255, 255, 255, 0.2) !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    font-weight: 600 !important;
    margin: 0 2px !important;
}

/* Hide elements that might interfere */
.picky-overlay.active ~ * {
    pointer-events: none !important;
}

/* Ensure the overlay works on all websites */
.picky-overlay,
.picky-magnifier,
.picky-color-info,
.picky-instructions {
    all: initial !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Animation for smooth appearance */
.picky-magnifier {
    transition: opacity 0.2s ease !important;
    opacity: 0 !important;
}

.picky-magnifier.active {
    opacity: 1 !important;
}

.picky-color-info {
    transition: opacity 0.2s ease !important;
    opacity: 0 !important;
}

.picky-color-info.active {
    opacity: 1 !important;
}

.picky-instructions {
    transition: opacity 0.3s ease !important;
    opacity: 0 !important;
}

.picky-instructions.active {
    opacity: 1 !important;
}
