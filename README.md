# 🎨 Picky - Color Picker Chrome Extension

A powerful and intuitive color picker Chrome extension that allows you to extract colors from any website and save them in multiple formats.

## Features

### 🎯 Color Picking
- **Universal Color Picker**: Extract colors from any element on any webpage
- **Visual Magnifier**: See exactly what you're picking with a magnifying glass overlay
- **Cross-Origin Support**: Works on all websites, including those with strict CSP policies
- **Real-time Preview**: See color values as you hover over elements

### 🎨 Multiple Color Formats
- **HEX**: `#FF5733`
- **RGB**: `rgb(255, 87, 51)`
- **HSL**: `hsl(14, 100%, 60%)`
- **HSV**: `hsv(14, 80%, 100%)`

### 📋 Clipboard Integration
- **Auto-copy**: Automatically copies HEX values to clipboard when picked
- **Manual Copy**: Click any format to copy to clipboard
- **Visual Feedback**: Clear confirmation when colors are copied

### 💾 Color Storage
- **Persistent Storage**: Save up to 50 colors using Chrome's local storage
- **Quick Access**: Click saved colors to copy them instantly
- **Color Management**: Delete individual colors or clear all at once
- **Visual Grid**: Beautiful grid layout for saved colors

### 🎪 User Experience
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Responsive Design**: Works perfectly in the popup window
- **Visual Feedback**: Status messages and hover effects
- **Keyboard Support**: ESC key to cancel color picking

## Installation

### From Chrome Web Store
1. Visit the Chrome Web Store (link coming soon)
2. Click "Add to Chrome"
3. Confirm the installation

### Manual Installation (Developer Mode)
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The Picky extension icon should appear in your toolbar

## Usage

### Picking Colors
1. Click the Picky extension icon in your toolbar
2. Click the "Pick Color" button
3. Move your cursor over any element on the webpage
4. Click to pick the color
5. The color will be automatically copied to your clipboard

### Changing Formats
1. After picking a color, use the format dropdown to switch between HEX, RGB, HSL, and HSV
2. Click the copy button or the color value to copy the current format

### Saving Colors
1. After picking a color, click the "Save Color" button
2. Saved colors appear in the grid below
3. Click any saved color to copy it to clipboard
4. Hover over saved colors to see the delete button

### Managing Saved Colors
- **Delete Individual**: Hover over a saved color and click the × button
- **Clear All**: Click the trash icon next to "Saved Colors" to clear all saved colors
- **Automatic Limit**: Only the 50 most recent colors are kept

## Technical Details

### Permissions
- `activeTab`: Required to inject the color picker into web pages
- `storage`: Required to save and retrieve color preferences
- `clipboardWrite`: Required to copy colors to clipboard

### Browser Compatibility
- Chrome 88+ (Manifest V3 support required)
- Chromium-based browsers (Edge, Brave, etc.)

### Privacy
- No data is sent to external servers
- All colors are stored locally in your browser
- No tracking or analytics

## Development

### Project Structure
```
picky/
├── manifest.json          # Extension manifest
├── popup.html            # Popup interface
├── popup.css             # Popup styling
├── popup.js              # Popup functionality
├── content.js            # Content script for color picking
├── content.css           # Content script styling
├── background.js         # Background service worker
├── color-utils.js        # Color conversion utilities
├── icons/                # Extension icons
└── README.md            # This file
```

### Building
No build process required - this is a pure JavaScript extension.

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

If you encounter any issues or have feature requests, please open an issue on GitHub.

---

Made with ❤️ for designers and developers who love colors!
