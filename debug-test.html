<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Picky Extension Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .color-box {
            width: 100px;
            height: 100px;
            margin: 10px;
            display: inline-block;
            border-radius: 8px;
            cursor: pointer;
        }
        .red { background-color: #ff4444; }
        .blue { background-color: #4444ff; }
        .green { background-color: #44ff44; }
        .yellow { background-color: #ffff44; }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Picky Extension Debug Test</h1>
        
        <div class="instructions">
            <h3>Debug Steps:</h3>
            <div class="step">
                <strong>Step 1:</strong> Open Chrome Developer Tools (F12)
            </div>
            <div class="step">
                <strong>Step 2:</strong> Go to the Console tab
            </div>
            <div class="step">
                <strong>Step 3:</strong> Click the Picky extension icon
            </div>
            <div class="step">
                <strong>Step 4:</strong> Click "Pick Color" button
            </div>
            <div class="step">
                <strong>Step 5:</strong> Check console for debug messages
            </div>
        </div>

        <div class="test-section">
            <h3>Test Colors</h3>
            <p>Try picking colors from these boxes:</p>
            <div class="color-box red" title="Red: #ff4444"></div>
            <div class="color-box blue" title="Blue: #4444ff"></div>
            <div class="color-box green" title="Green: #44ff44"></div>
            <div class="color-box yellow" title="Yellow: #ffff44"></div>
        </div>

        <div class="debug-info">
            <h4>Expected Console Messages:</h4>
            <pre>
1. Popup Console (when clicking Pick Color):
   - "Starting color picking..."
   - "Querying for active tab..."
   - "Found tabs: [...]"
   - "Active tab ID: [number]"
   - "Sending message to background script..."
   - "Background script response: {success: true}"

2. Background Console:
   - "Background received message: {action: 'startColorPicking', tabId: [number]}"
   - "Using tab: {id: [number]}"
   - "Starting color picking for tab: [number]"
   - "Color picking started successfully"

3. Content Script Console:
   - "Picky content script initialized"
   - "Content script received message: {action: 'startColorPicking'}"
   - "Starting color picking in content script"
            </pre>
        </div>

        <div class="test-section">
            <h3>Manual Extension Check</h3>
            <p>To verify the extension is loaded correctly:</p>
            <ol>
                <li>Go to <code>chrome://extensions/</code></li>
                <li>Find "Picky - Color Picker" in the list</li>
                <li>Make sure it's enabled (toggle should be blue)</li>
                <li>Click "Details" to see more information</li>
                <li>Check for any error messages</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Common Issues & Solutions</h3>
            
            <h4>Issue: "Cannot read properties of undefined (reading 'id')"</h4>
            <p><strong>Solution:</strong> This usually means the tab query failed. Check:</p>
            <ul>
                <li>Extension has "activeTab" permission</li>
                <li>You're testing on a regular webpage (not chrome:// pages)</li>
                <li>The extension is properly loaded</li>
            </ul>

            <h4>Issue: No console messages appear</h4>
            <p><strong>Solution:</strong> Check:</p>
            <ul>
                <li>Developer tools are open on the correct context</li>
                <li>Extension popup console vs page console vs background console</li>
                <li>Extension is enabled and loaded</li>
            </ul>

            <h4>Issue: Content script not injected</h4>
            <p><strong>Solution:</strong> Check:</p>
            <ul>
                <li>Extension has "scripting" permission</li>
                <li>Files exist: content.js, content.css, color-utils.js</li>
                <li>No JavaScript errors in background script</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Quick Test Script</h3>
            <p>Run this in the page console to test basic functionality:</p>
            <div class="debug-info">
                <pre>
// Test if extension is accessible
chrome.runtime.sendMessage({action: 'test'}, (response) => {
    console.log('Extension response:', response);
});

// Test tab query
chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    console.log('Current tab:', tabs[0]);
});
                </pre>
            </div>
        </div>
    </div>

    <script>
        // Add some basic debugging
        console.log('Debug test page loaded');
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
        
        // Test color boxes
        document.querySelectorAll('.color-box').forEach(box => {
            box.addEventListener('click', () => {
                const color = window.getComputedStyle(box).backgroundColor;
                console.log('Clicked color box, background color:', color);
            });
        });
    </script>
</body>
</html>
