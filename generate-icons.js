// Simple icon generator for Picky extension
// This creates basic colored squares as placeholder icons

const fs = require('fs');
const { createCanvas } = require('canvas');

function generateIcon(size, filename) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);
    
    // Add a simple color picker icon
    const scale = size / 48;
    const centerX = size / 2;
    const centerY = size / 2;
    
    // White circle background
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.beginPath();
    ctx.arc(centerX, centerY, size * 0.3, 0, 2 * Math.PI);
    ctx.fill();
    
    // Color picker symbol
    ctx.fillStyle = '#333';
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('🎨', centerX, centerY);
    
    // Save the image
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(`icons/${filename}`, buffer);
    console.log(`Generated ${filename}`);
}

// Generate all icon sizes
try {
    generateIcon(16, 'icon16.png');
    generateIcon(32, 'icon32.png');
    generateIcon(48, 'icon48.png');
    generateIcon(128, 'icon128.png');
    console.log('All icons generated successfully!');
} catch (error) {
    console.log('Canvas module not available. Creating simple placeholder icons...');
    
    // Create simple placeholder files
    const sizes = [16, 32, 48, 128];
    sizes.forEach(size => {
        const filename = `icons/icon${size}.png`;
        // Create a minimal PNG file (this is just a placeholder)
        const placeholder = `<!-- Placeholder for ${size}x${size} icon -->`;
        fs.writeFileSync(filename, placeholder);
        console.log(`Created placeholder ${filename}`);
    });
}
