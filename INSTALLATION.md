# 🎨 Picky Color Picker - Installation Guide

## Quick Installation (Developer Mode)

Since this extension is not yet published on the Chrome Web Store, you'll need to install it in developer mode.

### Step 1: Download the Extension
1. Download all the extension files to a folder on your computer
2. Make sure you have all these files in the same directory:
   - `manifest.json`
   - `popup.html`
   - `popup.css`
   - `popup.js`
   - `content.js`
   - `content.css`
   - `background.js`
   - `color-utils.js`
   - `icons/` folder with icon files

### Step 2: Enable Developer Mode in Chrome
1. Open Google Chrome
2. Go to `chrome://extensions/` (type this in the address bar)
3. In the top right corner, toggle on **"Developer mode"**

### Step 3: Load the Extension
1. Click the **"Load unpacked"** button that appears after enabling developer mode
2. Navigate to and select the folder containing the extension files
3. Click **"Select Folder"** (or "Open" on some systems)

### Step 4: Verify Installation
1. You should see the Picky extension appear in your extensions list
2. The Picky icon should appear in your Chrome toolbar
3. If you don't see the icon, click the puzzle piece icon (🧩) in the toolbar and pin Picky

## Testing the Extension

### Option 1: Use the Test Page
1. Open the included `test-page.html` file in Chrome
2. Click the Picky extension icon
3. Click "Pick Color" in the popup
4. Try picking colors from different elements on the test page

### Option 2: Test on Any Website
1. Navigate to any website
2. Click the Picky extension icon
3. Click "Pick Color"
4. Move your cursor over any element and click to pick its color

## Troubleshooting

### Extension Not Loading
- Make sure all files are in the same folder
- Check that `manifest.json` is present and valid
- Try refreshing the extensions page and reloading the extension

### Color Picker Not Working
- Make sure you clicked "Pick Color" in the popup first
- Try refreshing the webpage and trying again
- Check the browser console for any error messages

### Clipboard Not Working
- The extension will try multiple methods to copy to clipboard
- Make sure Chrome has permission to access the clipboard
- Some websites may block clipboard access due to security policies

### Icons Not Showing
- The extension will work even with missing icon files
- You can create your own 16x16, 32x32, 48x48, and 128x128 PNG icons
- Place them in the `icons/` folder with the correct names

## Features to Test

1. **Color Picking**: Pick colors from any element on any webpage
2. **Format Switching**: Switch between HEX, RGB, HSL, and HSV formats
3. **Auto-Copy**: HEX values are automatically copied when picked
4. **Manual Copy**: Click the copy button to copy any format
5. **Color Saving**: Save colors for later use
6. **Saved Color Management**: Click saved colors to copy them, delete individual colors, or clear all

## Browser Compatibility

- **Chrome 88+**: Full support (Manifest V3 required)
- **Edge**: Should work (Chromium-based)
- **Brave**: Should work (Chromium-based)
- **Firefox**: Not compatible (uses different extension system)
- **Safari**: Not compatible (uses different extension system)

## Security Notes

- The extension only accesses the current active tab when picking colors
- No data is sent to external servers
- All saved colors are stored locally in your browser
- The extension respects website security policies (CSP)

## Next Steps

Once you've tested the extension and confirmed it works:

1. **Report Issues**: If you find any bugs, note them down
2. **Suggest Features**: Think about additional features you'd like
3. **Create Real Icons**: Replace the placeholder icons with proper designs
4. **Prepare for Publishing**: Consider publishing to the Chrome Web Store

## Support

If you encounter any issues:

1. Check the browser console for error messages
2. Try disabling and re-enabling the extension
3. Make sure you're using a supported Chrome version
4. Test on different websites to isolate the issue

---

Happy color picking! 🎨
