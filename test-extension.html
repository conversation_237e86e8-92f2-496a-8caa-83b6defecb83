<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Picky Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
        }
        .color-test {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .color-box {
            height: 80px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .color-box:hover {
            transform: scale(1.05);
        }
        .red { background: #e74c3c; }
        .blue { background: #3498db; }
        .green { background: #2ecc71; }
        .yellow { background: #f1c40f; color: #333; }
        .purple { background: #9b59b6; }
        .orange { background: #e67e22; }
        .pink { background: #e91e63; }
        .teal { background: #1abc9c; }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Picky Extension Test Page</h1>
        
        <div class="status">
            <strong>✅ Ready to test!</strong> Click the Picky extension icon and then "Pick Color" to start.
        </div>

        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li><strong>Reload Extension:</strong> Go to chrome://extensions/ and reload Picky</li>
                <li><strong>Click Extension Icon:</strong> Look for Picky in your toolbar</li>
                <li><strong>Click "Pick Color":</strong> This should start the color picker</li>
                <li><strong>Pick Colors:</strong> Click on any colored element below</li>
                <li><strong>Check Clipboard:</strong> The color should be copied automatically</li>
            </ol>
        </div>

        <h3>Test Colors</h3>
        <div class="color-test">
            <div class="color-box red">#E74C3C</div>
            <div class="color-box blue">#3498DB</div>
            <div class="color-box green">#2ECC71</div>
            <div class="color-box yellow">#F1C40F</div>
            <div class="color-box purple">#9B59B6</div>
            <div class="color-box orange">#E67E22</div>
            <div class="color-box pink">#E91E63</div>
            <div class="color-box teal">#1ABC9C</div>
        </div>

        <h3>Debug Tools</h3>
        <button class="test-button" onclick="testExtensionConnection()">Test Extension Connection</button>
        <button class="test-button" onclick="checkClasses()">Check Classes Loaded</button>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>

        <div id="debug-output" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: monospace; font-size: 12px; display: none;">
        </div>

        <div class="instructions" style="margin-top: 30px;">
            <h3>Expected Behavior:</h3>
            <ul>
                <li>✅ No "ReferenceError" messages in console</li>
                <li>✅ No "already declared" errors</li>
                <li>✅ Color picker overlay appears when clicking "Pick Color"</li>
                <li>✅ Magnifier shows when hovering over elements</li>
                <li>✅ Colors are copied to clipboard when clicked</li>
                <li>✅ Extension popup shows picked colors</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message) {
            console.log(message);
            const output = document.getElementById('debug-output');
            output.style.display = 'block';
            output.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        }

        function testExtensionConnection() {
            log('Testing extension connection...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    chrome.runtime.sendMessage({action: 'test'}, (response) => {
                        if (chrome.runtime.lastError) {
                            log('❌ Extension connection failed: ' + chrome.runtime.lastError.message);
                        } else {
                            log('✅ Extension connection successful');
                        }
                    });
                } catch (error) {
                    log('❌ Error testing connection: ' + error.message);
                }
            } else {
                log('❌ Chrome extension APIs not available');
            }
        }

        function checkClasses() {
            log('Checking if classes are loaded...');
            
            // Check if classes exist in window
            if (typeof window.ColorUtils !== 'undefined') {
                log('✅ ColorUtils is available');
            } else {
                log('❌ ColorUtils not found');
            }
            
            if (typeof window.PickyColorPicker !== 'undefined') {
                log('✅ PickyColorPicker class is available');
            } else {
                log('❌ PickyColorPicker class not found');
            }
            
            if (typeof window.pickyColorPicker !== 'undefined') {
                log('✅ pickyColorPicker instance is available');
            } else {
                log('❌ pickyColorPicker instance not found');
            }
        }

        function clearConsole() {
            console.clear();
            document.getElementById('debug-output').innerHTML = '';
            document.getElementById('debug-output').style.display = 'none';
            log('Console cleared');
        }

        // Add click handlers to color boxes for testing
        document.querySelectorAll('.color-box').forEach(box => {
            box.addEventListener('click', () => {
                const color = window.getComputedStyle(box).backgroundColor;
                log('Clicked color box: ' + color);
            });
        });

        // Log page load
        log('Test page loaded successfully');
    </script>
</body>
</html>
