* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 16px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header h1 {
    color: white;
    font-size: 24px;
    font-weight: 600;
}

.pick-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.pick-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.pick-icon {
    font-size: 16px;
}

/* Current Color Section */
.current-color-section {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.color-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.color-swatch {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: none;
}

.no-color {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 30px 0;
}

.format-selector {
    margin-bottom: 12px;
}

.format-selector select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.color-value-container {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.color-value-container input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #f8f9fa;
}

.copy-btn {
    padding: 10px 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.copy-btn:hover {
    background: #45a049;
}

.save-btn {
    width: 100%;
    padding: 12px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.save-btn:hover {
    background: #1976D2;
}

/* Saved Colors Section */
.saved-colors-section {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.section-header h3 {
    font-size: 16px;
    color: #333;
}

.clear-all-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.clear-all-btn:hover {
    background: #d32f2f;
}

.saved-colors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 8px;
    max-height: 120px;
    overflow-y: auto;
}

.saved-color-item {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
    position: relative;
}

.saved-color-item:hover {
    transform: scale(1.1);
    border-color: #2196F3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.saved-color-item .delete-btn {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 16px;
    height: 16px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 10px;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
}

.saved-color-item:hover .delete-btn {
    display: flex;
}

.no-saved-colors {
    grid-column: 1 / -1;
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px 0;
}

/* Status Messages */
.status-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.status-message.show {
    opacity: 1;
}

.status-message.error {
    background: #f44336;
}

/* Scrollbar Styling */
.saved-colors-grid::-webkit-scrollbar {
    width: 4px;
}

.saved-colors-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.saved-colors-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.saved-colors-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
