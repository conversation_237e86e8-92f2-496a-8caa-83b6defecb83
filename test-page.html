<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Picky Color Picker Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .color-box {
            height: 100px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .color-box:hover {
            transform: scale(1.05);
        }

        .red { background-color: #e74c3c; }
        .blue { background-color: #3498db; }
        .green { background-color: #2ecc71; }
        .yellow { background-color: #f1c40f; }
        .purple { background-color: #9b59b6; }
        .orange { background-color: #e67e22; }
        .pink { background-color: #e91e63; }
        .teal { background-color: #1abc9c; }

        .gradient-box {
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .text-colors {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }

        .text-sample {
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
        }

        .dark-text {
            background-color: #2c3e50;
            color: #ecf0f1;
        }

        .light-text {
            background-color: #ecf0f1;
            color: #2c3e50;
        }

        .accent-text {
            background-color: #f39c12;
            color: #ffffff;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            margin: 30px 0;
        }

        .instructions h3 {
            margin-top: 0;
            color: #007bff;
        }

        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 8px 0;
        }

        .complex-element {
            background: radial-gradient(circle at 30% 70%, #ff6b6b, #4ecdc4);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .complex-element::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .nested-colors {
            display: flex;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .nested-colors > div {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
        }

        .nested-1 { background-color: #ff4757; }
        .nested-2 { background-color: #2ed573; }
        .nested-3 { background-color: #1e90ff; }
        .nested-4 { background-color: #ffa502; }
        .nested-5 { background-color: #ff6348; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Picky Color Picker Test Page</h1>
        
        <div class="instructions">
            <h3>How to Test the Color Picker:</h3>
            <ol>
                <li>Install the Picky extension in Chrome</li>
                <li>Click the Picky extension icon in your toolbar</li>
                <li>Click the "Pick Color" button in the popup</li>
                <li>Move your cursor over any colored element on this page</li>
                <li>Click to pick the color</li>
                <li>The color will be automatically copied to your clipboard!</li>
            </ol>
        </div>

        <h2>Solid Colors</h2>
        <div class="color-grid">
            <div class="color-box red">#E74C3C</div>
            <div class="color-box blue">#3498DB</div>
            <div class="color-box green">#2ECC71</div>
            <div class="color-box yellow">#F1C40F</div>
            <div class="color-box purple">#9B59B6</div>
            <div class="color-box orange">#E67E22</div>
            <div class="color-box pink">#E91E63</div>
            <div class="color-box teal">#1ABC9C</div>
        </div>

        <h2>Gradient Background</h2>
        <div class="gradient-box">
            Linear Gradient: #667eea → #764ba2
        </div>

        <h2>Text Colors</h2>
        <div class="text-colors">
            <div class="text-sample dark-text">Dark Background (#2c3e50)</div>
            <div class="text-sample light-text">Light Background (#ecf0f1)</div>
            <div class="text-sample accent-text">Accent Background (#f39c12)</div>
        </div>

        <h2>Complex Element</h2>
        <div class="complex-element">
            <p style="color: white; font-weight: bold; margin: 0;">
                This element has a radial gradient background with an animated shine effect.
                Try picking colors from different parts!
            </p>
        </div>

        <h2>Nested Color Sections</h2>
        <div class="nested-colors">
            <div class="nested-1">#ff4757</div>
            <div class="nested-2">#2ed573</div>
            <div class="nested-3">#1e90ff</div>
            <div class="nested-4">#ffa502</div>
            <div class="nested-5">#ff6348</div>
        </div>

        <div style="margin-top: 40px; text-align: center; color: #666;">
            <p>This test page contains various color elements to test the Picky color picker extension.</p>
            <p>Try picking colors from different elements and see how the extension handles various scenarios!</p>
        </div>
    </div>
</body>
</html>
