<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Picky Extension Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        .color-test {
            width: 100px;
            height: 100px;
            background: #e74c3c;
            border-radius: 8px;
            margin: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Picky Extension Connection Debug</h1>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0; border: 1px solid #ffeaa7;">
            <strong>Instructions:</strong>
            <ol>
                <li>Make sure the Picky extension is loaded and enabled</li>
                <li>Reload the extension if you made changes</li>
                <li>Run the tests below to diagnose connection issues</li>
            </ol>
        </div>

        <div>
            <button class="test-button" onclick="testBasicConnection()">1. Test Basic Connection</button>
            <button class="test-button" onclick="testScriptInjection()">2. Test Script Injection</button>
            <button class="test-button" onclick="testColorPicking()">3. Test Color Picking</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="color-test" onclick="testColorClick()">
            Click me!<br>#E74C3C
        </div>

        <div id="log" class="log">
            <div class="info">Debug log will appear here...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = type;
            entry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">Log cleared...</div>';
        }

        async function testBasicConnection() {
            log('Testing basic extension connection...', 'info');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('❌ Chrome extension APIs not available', 'error');
                return;
            }

            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log('✅ Extension connection successful', 'success');
                log(`Response: ${JSON.stringify(response)}`, 'info');
            } catch (error) {
                log(`❌ Extension connection failed: ${error.message}`, 'error');
            }
        }

        async function testScriptInjection() {
            log('Testing script injection...', 'info');
            
            try {
                // First test if we can send a message to background
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        action: 'startColorPicking',
                        tabId: 'test'
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log('✅ Background script responded', 'success');
                log(`Response: ${JSON.stringify(response)}`, 'info');
                
                // Wait a moment then check if content script was injected
                setTimeout(() => {
                    if (typeof window.pickyColorPicker !== 'undefined') {
                        log('✅ Content script injected successfully', 'success');
                        log('✅ pickyColorPicker instance found', 'success');
                    } else {
                        log('❌ Content script not found', 'error');
                    }
                    
                    if (typeof window.ColorUtils !== 'undefined') {
                        log('✅ ColorUtils loaded successfully', 'success');
                    } else {
                        log('❌ ColorUtils not found', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                log(`❌ Script injection test failed: ${error.message}`, 'error');
            }
        }

        async function testColorPicking() {
            log('Testing color picking functionality...', 'info');
            
            if (typeof window.pickyColorPicker === 'undefined') {
                log('❌ pickyColorPicker not found. Run script injection test first.', 'error');
                return;
            }
            
            try {
                log('Starting color picking mode...', 'info');
                window.pickyColorPicker.startColorPicking();
                log('✅ Color picking started successfully', 'success');
                log('You should see the color picker overlay now', 'info');
            } catch (error) {
                log(`❌ Color picking failed: ${error.message}`, 'error');
            }
        }

        function testColorClick() {
            log('Color box clicked!', 'info');
            const color = window.getComputedStyle(document.querySelector('.color-test')).backgroundColor;
            log(`Computed color: ${color}`, 'info');
        }

        // Auto-run basic connection test on load
        window.addEventListener('load', () => {
            log('Page loaded, running basic connection test...', 'info');
            setTimeout(testBasicConnection, 500);
        });

        // Listen for messages from content script
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                log(`Received message: ${JSON.stringify(request)}`, 'info');
                return true;
            });
        }
    </script>
</body>
</html>
