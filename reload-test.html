<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Picky Extension - Reload Safe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        .test-button:hover { background: #0056b3; }
        .test-button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .color-box {
            height: 100px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
            text-align: center;
            font-size: 14px;
        }
        .color-box:hover { transform: scale(1.05); }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Picky Extension - Reload Safe Test</h1>
        
        <div id="extension-status" class="status info">
            🔄 Checking extension status...
        </div>

        <div class="status warning">
            <strong>🔧 Development Mode Instructions:</strong><br>
            1. If you see "Extension context invalidated" errors, this is normal during development<br>
            2. Simply reload the extension in chrome://extensions/<br>
            3. Refresh this page after reloading the extension<br>
            4. The extension will automatically recover from context invalidation
        </div>

        <div>
            <button class="test-button" onclick="testExtension()">🧪 Test Extension</button>
            <button class="test-button" onclick="startColorPicking()">🎯 Start Color Picking</button>
            <button class="test-button" onclick="checkExtensionHealth()">💊 Check Health</button>
            <button class="test-button" onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <h3>🎨 Test Colors (Click to pick after starting color picker)</h3>
        <div class="color-grid">
            <div class="color-box" style="background: #e74c3c;">Red<br>#E74C3C</div>
            <div class="color-box" style="background: #3498db;">Blue<br>#3498DB</div>
            <div class="color-box" style="background: #2ecc71;">Green<br>#2ECC71</div>
            <div class="color-box" style="background: #f39c12;">Orange<br>#F39C12</div>
            <div class="color-box" style="background: #9b59b6;">Purple<br>#9B59B6</div>
            <div class="color-box" style="background: #1abc9c;">Teal<br>#1ABC9C</div>
        </div>

        <div id="log" class="log">
            <div style="color: #007bff;">[INFO] Page loaded, checking extension...</div>
        </div>
    </div>

    <script>
        let logCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const colors = {
                info: '#007bff',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const entry = document.createElement('div');
            entry.style.color = colors[type] || colors.info;
            entry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // Limit log entries
            if (++logCount > 50) {
                logDiv.removeChild(logDiv.firstChild);
                logCount--;
            }
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            logCount = 0;
            log('Log cleared');
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('extension-status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        async function checkExtensionHealth() {
            log('Checking extension health...', 'info');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('❌ Chrome extension APIs not available', 'error');
                updateStatus('❌ Extension APIs not available', 'error');
                return false;
            }

            try {
                // Test if extension context is valid
                const extensionId = chrome.runtime.id;
                log(`✅ Extension ID: ${extensionId}`, 'success');
                
                // Test background script connection
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => reject(new Error('Timeout')), 3000);
                    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log('✅ Background script responding', 'success');
                updateStatus('✅ Extension is healthy and ready', 'success');
                return true;
                
            } catch (error) {
                log(`❌ Extension health check failed: ${error.message}`, 'error');
                updateStatus(`❌ Extension issue: ${error.message}`, 'error');
                return false;
            }
        }

        async function testExtension() {
            log('Running comprehensive extension test...', 'info');
            
            const isHealthy = await checkExtensionHealth();
            if (!isHealthy) {
                log('❌ Extension health check failed, aborting test', 'error');
                return;
            }
            
            // Test content script injection
            try {
                log('Testing content script injection...', 'info');
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({action: 'startColorPicking'}, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log('✅ Content script injection successful', 'success');
                
                // Wait and check if content script is available
                setTimeout(() => {
                    if (typeof window.pickyColorPicker !== 'undefined') {
                        log('✅ Color picker instance available', 'success');
                        updateStatus('✅ Extension fully functional', 'success');
                    } else {
                        log('⚠️ Color picker instance not found', 'warning');
                        updateStatus('⚠️ Extension partially functional', 'warning');
                    }
                }, 1000);
                
            } catch (error) {
                log(`❌ Content script test failed: ${error.message}`, 'error');
                updateStatus(`❌ Content script issue: ${error.message}`, 'error');
            }
        }

        async function startColorPicking() {
            log('Starting color picking mode...', 'info');
            
            if (typeof window.pickyColorPicker !== 'undefined') {
                try {
                    window.pickyColorPicker.startColorPicking();
                    log('✅ Color picking started (using existing instance)', 'success');
                    updateStatus('🎯 Color picking active - click on colors!', 'info');
                } catch (error) {
                    log(`❌ Failed to start color picking: ${error.message}`, 'error');
                }
            } else {
                log('Color picker not available, running test first...', 'warning');
                await testExtension();
                setTimeout(() => {
                    if (typeof window.pickyColorPicker !== 'undefined') {
                        window.pickyColorPicker.startColorPicking();
                        log('✅ Color picking started (after injection)', 'success');
                        updateStatus('🎯 Color picking active - click on colors!', 'info');
                    }
                }, 1500);
            }
        }

        // Auto-check extension health on page load
        window.addEventListener('load', () => {
            setTimeout(checkExtensionHealth, 500);
        });

        // Listen for extension messages
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            try {
                chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                    log(`Received message: ${JSON.stringify(request)}`, 'info');
                    return true;
                });
            } catch (error) {
                log(`Failed to set up message listener: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
