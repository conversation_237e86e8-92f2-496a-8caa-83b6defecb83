// Content script for Picky color picker extension

class PickyColorPicker {
    constructor() {
        this.isActive = false;
        this.overlay = null;
        this.magnifier = null;
        this.colorInfo = null;
        this.instructions = null;
        this.canvas = null;
        this.ctx = null;
        this.currentColor = null;
        
        this.init();
    }

    init() {
        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'startColorPicking') {
                this.startColorPicking();
                sendResponse({ success: true });
            } else if (request.action === 'stopColorPicking') {
                this.stopColorPicking();
                sendResponse({ success: true });
            }
        });

        // Load color utilities
        this.loadColorUtils();
    }

    loadColorUtils() {
        // Inject color-utils.js if not already loaded
        if (typeof ColorUtils === 'undefined') {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('color-utils.js');
            script.onload = () => {
                console.log('ColorUtils loaded successfully');
            };
            document.head.appendChild(script);
        }
    }

    startColorPicking() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.createOverlay();
        this.createMagnifier();
        this.createColorInfo();
        this.createInstructions();
        this.addEventListeners();
        
        // Show instructions
        this.showInstructions();
        
        // Hide cursor and show crosshair
        document.body.style.cursor = 'none';
    }

    stopColorPicking() {
        if (!this.isActive) return;
        
        this.isActive = false;
        this.removeOverlay();
        this.removeEventListeners();
        
        // Restore cursor
        document.body.style.cursor = '';
    }

    createOverlay() {
        this.overlay = document.createElement('div');
        this.overlay.className = 'picky-overlay';
        this.overlay.classList.add('active');
        document.body.appendChild(this.overlay);
    }

    createMagnifier() {
        this.magnifier = document.createElement('div');
        this.magnifier.className = 'picky-magnifier';
        
        this.canvas = document.createElement('canvas');
        this.canvas.className = 'picky-magnifier-canvas';
        this.canvas.width = 120;
        this.canvas.height = 120;
        this.ctx = this.canvas.getContext('2d');
        
        const crosshair = document.createElement('div');
        crosshair.className = 'picky-crosshair';
        
        this.magnifier.appendChild(this.canvas);
        this.magnifier.appendChild(crosshair);
        document.body.appendChild(this.magnifier);
    }

    createColorInfo() {
        this.colorInfo = document.createElement('div');
        this.colorInfo.className = 'picky-color-info';
        document.body.appendChild(this.colorInfo);
    }

    createInstructions() {
        this.instructions = document.createElement('div');
        this.instructions.className = 'picky-instructions';
        this.instructions.innerHTML = `
            <div>Click to pick a color • <span class="key">ESC</span> to cancel</div>
        `;
        document.body.appendChild(this.instructions);
    }

    showInstructions() {
        if (this.instructions) {
            this.instructions.classList.add('active');
            setTimeout(() => {
                if (this.instructions) {
                    this.instructions.classList.remove('active');
                }
            }, 3000);
        }
    }

    addEventListeners() {
        this.mouseMoveHandler = this.handleMouseMove.bind(this);
        this.clickHandler = this.handleClick.bind(this);
        this.keyHandler = this.handleKeyPress.bind(this);
        
        document.addEventListener('mousemove', this.mouseMoveHandler, true);
        document.addEventListener('click', this.clickHandler, true);
        document.addEventListener('keydown', this.keyHandler, true);
    }

    removeEventListeners() {
        document.removeEventListener('mousemove', this.mouseMoveHandler, true);
        document.removeEventListener('click', this.clickHandler, true);
        document.removeEventListener('keydown', this.keyHandler, true);
    }

    removeOverlay() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
        if (this.magnifier) {
            this.magnifier.remove();
            this.magnifier = null;
        }
        if (this.colorInfo) {
            this.colorInfo.remove();
            this.colorInfo = null;
        }
        if (this.instructions) {
            this.instructions.remove();
            this.instructions = null;
        }
    }

    handleMouseMove(event) {
        if (!this.isActive) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        const x = event.clientX;
        const y = event.clientY;
        
        this.updateMagnifier(x, y);
        this.updateColorInfo(x, y);
    }

    handleClick(event) {
        if (!this.isActive) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        const x = event.clientX;
        const y = event.clientY;
        
        this.pickColor(x, y);
    }

    handleKeyPress(event) {
        if (!this.isActive) return;
        
        if (event.key === 'Escape') {
            event.preventDefault();
            event.stopPropagation();
            this.stopColorPicking();
            
            // Notify popup that picking was cancelled
            chrome.runtime.sendMessage({
                action: 'colorPickingCancelled'
            });
        }
    }

    updateMagnifier(x, y) {
        if (!this.magnifier || !this.canvas || !this.ctx) return;
        
        // Position magnifier
        const magnifierSize = 120;
        const offset = 20;
        let magnifierX = x + offset;
        let magnifierY = y - magnifierSize - offset;
        
        // Keep magnifier within viewport
        if (magnifierX + magnifierSize > window.innerWidth) {
            magnifierX = x - magnifierSize - offset;
        }
        if (magnifierY < 0) {
            magnifierY = y + offset;
        }
        
        this.magnifier.style.left = magnifierX + 'px';
        this.magnifier.style.top = magnifierY + 'px';
        this.magnifier.classList.add('active');
        
        // Capture area around cursor
        this.captureArea(x, y);
    }

    captureArea(centerX, centerY) {
        try {
            // Create a temporary canvas to capture the screen area
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            const captureSize = 20;
            const scale = 6;
            
            tempCanvas.width = captureSize;
            tempCanvas.height = captureSize;
            
            // Use html2canvas-like approach for cross-origin compatibility
            this.captureElementsAtPoint(centerX, centerY, tempCanvas, tempCtx, captureSize);
            
            // Scale up the captured area
            this.ctx.imageSmoothingEnabled = false;
            this.ctx.clearRect(0, 0, 120, 120);
            this.ctx.drawImage(tempCanvas, 0, 0, captureSize, captureSize, 0, 0, 120, 120);
            
        } catch (error) {
            console.warn('Could not capture area:', error);
        }
    }

    captureElementsAtPoint(x, y, canvas, ctx, size) {
        const halfSize = Math.floor(size / 2);
        
        // Get elements at the point and surrounding area
        for (let dy = -halfSize; dy < halfSize; dy++) {
            for (let dx = -halfSize; dx < halfSize; dx++) {
                const element = document.elementFromPoint(x + dx, y + dy);
                if (element) {
                    const computedStyle = window.getComputedStyle(element);
                    const bgColor = computedStyle.backgroundColor;
                    
                    if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                        ctx.fillStyle = bgColor;
                        ctx.fillRect(dx + halfSize, dy + halfSize, 1, 1);
                    }
                }
            }
        }
    }

    updateColorInfo(x, y) {
        if (!this.colorInfo) return;
        
        const color = this.getColorAtPoint(x, y);
        if (color) {
            this.currentColor = color;
            
            const colorPreview = document.createElement('span');
            colorPreview.className = 'picky-color-preview';
            colorPreview.style.backgroundColor = `rgb(${color.r}, ${color.g}, ${color.b})`;
            
            this.colorInfo.innerHTML = '';
            this.colorInfo.appendChild(colorPreview);

            // Use ColorUtils if available, otherwise use fallback
            const hexColor = typeof ColorUtils !== 'undefined'
                ? ColorUtils.rgbToHex(color.r, color.g, color.b)
                : this.rgbToHexFallback(color.r, color.g, color.b);

            this.colorInfo.appendChild(document.createTextNode(hexColor));
            
            // Position color info
            let infoX = x + 15;
            let infoY = y - 30;
            
            if (infoX + 100 > window.innerWidth) {
                infoX = x - 100;
            }
            if (infoY < 0) {
                infoY = y + 15;
            }
            
            this.colorInfo.style.left = infoX + 'px';
            this.colorInfo.style.top = infoY + 'px';
            this.colorInfo.classList.add('active');
        }
    }

    getColorAtPoint(x, y) {
        try {
            const element = document.elementFromPoint(x, y);
            if (element) {
                const computedStyle = window.getComputedStyle(element);
                const bgColor = computedStyle.backgroundColor;
                
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    return this.parseColor(bgColor);
                }
                
                // If no background color, try to get color from canvas if available
                if (this.canvas && this.ctx) {
                    const imageData = this.ctx.getImageData(60, 60, 1, 1);
                    return {
                        r: imageData.data[0],
                        g: imageData.data[1],
                        b: imageData.data[2]
                    };
                }
            }
        } catch (error) {
            console.warn('Could not get color at point:', error);
        }
        
        return { r: 255, g: 255, b: 255 }; // Default to white
    }

    parseColor(colorString) {
        const rgbMatch = colorString.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }
        return null;
    }

    pickColor(x, y) {
        const color = this.getColorAtPoint(x, y);
        if (color) {
            // Send color to popup
            chrome.runtime.sendMessage({
                action: 'colorPicked',
                color: color
            });

            this.stopColorPicking();
        }
    }

    // Fallback method for RGB to HEX conversion
    rgbToHexFallback(r, g, b) {
        const toHex = (n) => {
            const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }
}

// Initialize the color picker when the content script loads
let pickyColorPicker;

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        pickyColorPicker = new PickyColorPicker();
    });
} else {
    pickyColorPicker = new PickyColorPicker();
}
